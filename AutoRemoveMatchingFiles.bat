@echo off
setlocal enabledelayedexpansion

:: Set the paths for the configuration files
set "searchFile=SearchedFolder.txt"
set "removalFile=RemovalList.txt"

:: Ensure both files exist
if not exist "%searchFile%" (
    echo Error: %searchFile% not found.
    pause
    exit /b
)
if not exist "%removalFile%" (
    echo Error: %removalFile% not found.
    pause
    exit /b
)

:: Read the folder path from SearchedFolder.txt
set /p "targetFolder=" < "%searchFile%"
set "targetFolder=%targetFolder:\"=%"

:: Check if the folder exists
if not exist "%targetFolder%" (
    echo Error: Folder "%targetFolder%" does not exist.
    pause
    exit /b
)

:: Create a list of all files in the folder
set "fileList=%temp%\\fileList.txt"
dir /b /a-d "%targetFolder%" > "%fileList%"

:: Find matching files using findstr
set "matches=%temp%\\matches.txt"
findstr /i /g:"%removalFile%" "%fileList%" > "%matches%" 2>nul

:: If no matches found, exit
if not exist "%matches%" (
    echo No matching files found.
    del "%fileList%" 2>nul
    pause
    exit /b
)

:: Display matched files
echo The following files matched:
type "%matches%"

:: Confirm deletion
set /p "confirm=Do you want to delete the matching files? (y/n): "
if /i not "%confirm%"=="y" (
    echo No files were deleted.
    del "%fileList%" "%matches%" 2>nul
    pause
    exit /b
)

:: Delete the matched files
for /f "usebackq delims=" %%A in ("%matches%") do (
    echo Deleting: "%targetFolder%\\%%A"
    del "%targetFolder%\\%%A"
)

:: Cleanup and finish
del "%fileList%" "%matches%" 2>nul
echo Files deleted successfully.
pause